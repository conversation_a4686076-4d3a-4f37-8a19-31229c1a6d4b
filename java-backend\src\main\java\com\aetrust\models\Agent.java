package com.aetrust.models;

import com.aetrust.types.Types.AgentStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agents")
public class Agent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String userId;

    @Column(unique = true, nullable = false)
    private String agentCode;
    
    private String businessName;
    private String businessType;
    private String businessRegistrationNumber;
    private String taxId;
    
    private Location location;
    private ContactInfo contactInfo;
    private OperatingHours operatingHours;
    private Services services;
    private Compliance compliance;
    private Performance performance;
    
    private AgentStatus status = AgentStatus.PENDING;
    private Double commissionRate = 0.0;
    private Double totalCommissions = 0.0;
    private Double cashBalance = 0.0;
    private Double floatBalance = 0.0;
    
    private String approvedBy;
    private LocalDateTime approvedAt;
    private String rejectionReason;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    // nested classes for embedded documents
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Location {
        private String address;
        private String city;
        private String state;
        private String country;
        private String postalCode;
        private Coordinates coordinates;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Coordinates {
        private Double latitude;
        private Double longitude;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {
        private String phone;
        private String email;
        private String whatsapp;
        private String alternatePhone;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperatingHours {
        private Integer startHour = 8;
        private Integer endHour = 20;
        private List<String> workingDays;
        private boolean isOpen24Hours = false;
        private Map<String, String> specialHours;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Services {
        private boolean cashIn = true;
        private boolean cashOut = true;
        private boolean billPayments = false;
        private boolean mobileTopup = false;
        private boolean moneyTransfer = true;
        private List<String> supportedCurrencies;
        private Double dailyLimit = 50000.0;
        private Double transactionLimit = 5000.0;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Compliance {
        private boolean kycCompleted = false;
        private boolean documentsVerified = false;
        private boolean backgroundCheckPassed = false;
        private LocalDateTime lastAudit;
        private String auditStatus;
        private List<String> requiredDocuments;
        private List<String> submittedDocuments;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Performance {
        private Integer totalTransactions = 0;
        private Double totalVolume = 0.0;
        private Double averageRating = 0.0;
        private Integer totalRatings = 0;
        private Integer successfulTransactions = 0;
        private Integer failedTransactions = 0;
        private Double successRate = 0.0;
        private LocalDateTime lastTransaction;
        private MonthlyStats monthlyStats;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyStats {
        private Integer transactionCount = 0;
        private Double transactionVolume = 0.0;
        private Double commissionsEarned = 0.0;
        private String month;
        private Integer year;
    }
    
    // helper methods
    public boolean isActive() {
        return status == AgentStatus.ACTIVE;
    }
    
    public boolean canPerformTransaction(Double amount) {
        if (!isActive()) return false;
        if (services == null) return false;
        return amount <= services.getTransactionLimit();
    }
    
    public String getDisplayName() {
        return businessName != null ? businessName : "Agent " + agentCode;
    }
}
