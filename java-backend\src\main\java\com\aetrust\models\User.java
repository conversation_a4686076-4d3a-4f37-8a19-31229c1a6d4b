package com.aetrust.models;

import com.aetrust.types.Types.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "users")
@EntityListeners(AuditingEntityListener.class)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String email;
    
    @JsonIgnore
    private String password;

    @JsonIgnore
    private String transactionPin;

    @Column(unique = true, nullable = false)
    private String phone;

    @Column(unique = true)
    private String username;
    
    private String firstName;
    private String lastName;
    private String profilePicture;
    private String bio;
    private LocalDate dateOfBirth;
    
    @Embedded
    private Address address;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole role = UserRole.USER;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private KycStatus kycStatus = KycStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AccountStatus accountStatus = AccountStatus.ACTIVE;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RegistrationStep registrationStep = RegistrationStep.PHONE_VERIFICATION;
    
    private boolean isVerified = false;
    private boolean phoneVerified = false;
    private boolean emailVerified = false;
    private boolean transactionPinSet = false;
    private boolean biometricEnabled = false;
    
    private Double walletBalance = 0.0;
    private String preferredCurrency = "USD";
    
    @Embedded
    private IdentityVerification identityVerification;

    @Embedded
    private Security security;

    @Embedded
    private AgentInfo agentInfo;

    @Embedded
    private Preferences preferences;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class Address {
        private String street;
        private String city;
        private String state;
        private String country;
        private String postalCode;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class IdentityVerification {
        private VerificationStatus verificationStatus = VerificationStatus.PENDING;
        private IdType idType;
        private String idNumber;
        private String idDocumentFront;
        private String idDocumentBack;
        private String selfiePhoto;
        private LocalDateTime verifiedAt;
        private String verifiedBy;
        private String rejectionReason;
        private Integer attemptCount = 0;
        private LocalDateTime lastAttempt;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class Security {
        private LocalDateTime lastLogin;
        private String lastLoginIp;
        private String lastLoginUserAgent;
        private Integer loginAttempts = 0;
        private LocalDateTime lockedUntil;
        private boolean twoFactorEnabled = false;
        private String twoFactorSecret;
        @Column(columnDefinition = "TEXT")
        private String backupCodes; // JSON string

        @Column(columnDefinition = "TEXT")
        private String securityEvents; // JSON string
        private String passwordResetToken;
        private LocalDateTime passwordResetExpires;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SecurityEvent {
        private String eventType;
        private String description;
        private String ipAddress;
        private String userAgent;
        private LocalDateTime timestamp;
        @Column(columnDefinition = "TEXT")
        private String metadata; // JSON string
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class AgentInfo {
        private String agentCode;
        private String businessName;
        private String businessType;
        private AgentStatus status = AgentStatus.PENDING;
        private Double commissionRate = 0.0;
        private Double totalCommissions = 0.0;
        private LocalDateTime approvedAt;
        private String approvedBy;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class Preferences {
        private String language = "en";
        private String timezone = "UTC";
        private boolean emailNotifications = true;
        private boolean smsNotifications = true;
        private boolean pushNotifications = true;
        private String theme = "light";
    }
    
    // helper methods
    public String getFullName() {
        if (firstName == null && lastName == null) return null;
        if (firstName == null) return lastName;
        if (lastName == null) return firstName;
        return firstName + " " + lastName;
    }
    
    public boolean isAgent() {
        return role == UserRole.AGENT && agentInfo != null;
    }
    
    public boolean isAdmin() {
        return role == UserRole.ADMIN || role == UserRole.SUPER_ADMIN;
    }
}
