server:
  port: ${PORT:3000}
  servlet:
    context-path: /api/v1

spring:
  application:
    name: aetrust-backend
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  
  datasource:
    url: ${DATABASE_URL:postgresql://aetrustdatabase_user:<EMAIL>/aetrustdatabase}
    username: ${DB_USERNAME:aetrustdatabase_user}
    password: ${DB_PASSWORD:5k9pX32rQcLdVGnkLYhTvC60wUTvKxrW}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DB_CONNECTION_POOL_SIZE:10}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: ${DDL_AUTO:update}
    properties:
      hibernate:
        format_sql: true
        show_sql: ${SHOW_SQL:false}
        jdbc:
          time_zone: UTC
      
  # Redis Configuration
  redis:
    enabled: ${REDIS_ENABLED:false}
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}
    timeout: ${REDIS_CONNECT_TIMEOUT:10000}ms
    lettuce:
      pool:
        max-active: ${REDIS_POOL_MAX_ACTIVE:10}
        max-idle: ${REDIS_POOL_MAX_IDLE:10}
        min-idle: ${REDIS_POOL_MIN_IDLE:1}
      shutdown-timeout: ${REDIS_COMMAND_TIMEOUT:5000}ms
        
  security:
    jwt:
      secret: ${JWT_SECRET:super-secret-jwt-key}
      refresh-secret: ${JWT_REFRESH_SECRET:super-secret-refresh-key}
      expiration: ${JWT_EXPIRATION:86400000}
      refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000}
      expires-in: ${JWT_EXPIRES_IN:15m}
      refresh-expires-in: ${JWT_REFRESH_EXPIRES_IN:7d}
      
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL
    
logging:
  level:
    com.aetrust: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.springframework.data.mongodb: ${DB_LOG_LEVEL:INFO}
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr(%m){green}%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/aetrust-backend.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

aetrust:
  security:
    salt-rounds: ${BCRYPT_SALT_ROUNDS:12}
    encryption-key: ${ENCRYPTION_KEY:your-encryption-key-32-chars-long}
    max-login-attempts: ${MAX_LOGIN_ATTEMPTS:5}
    lockout-duration: ${LOCKOUT_DURATION:900000}
    rate-limit:
      ip-limit: ${IP_RATE_LIMIT:100}
      user-limit: ${USER_RATE_LIMIT:50}
      window: ${RATE_LIMIT_WINDOW:3600000}
  
  fraud:
    velocity-threshold: ${FRAUD_VELOCITY_THRESHOLD:5}
    amount-threshold: ${FRAUD_AMOUNT_THRESHOLD:10000}
    location-check-enabled: ${FRAUD_LOCATION_CHECK:true}
    
  file:
    upload:
      max-size: ${MAX_FILE_SIZE:10MB}
      allowed-types: jpg,jpeg,png,pdf
      
  notification:
    sms:
      provider: ${SMS_PROVIDER:twilio}
      api-key: ${SMS_API_KEY:}
    email:
      provider: ${EMAIL_PROVIDER:sendgrid}
      api-key: ${EMAIL_API_KEY:}

  # Feature Flags for Security and Functionality
  feature-flags:
    threat-intelligence:
      enabled: ${FEATURE_THREAT_INTELLIGENCE:true}
    device-fingerprinting:
      enabled: ${FEATURE_DEVICE_FINGERPRINTING:true}
    geo-blocking:
      enabled: ${FEATURE_GEO_BLOCKING:true}
    behavioral-analysis:
      enabled: ${FEATURE_BEHAVIORAL_ANALYSIS:true}
    advanced-logging:
      enabled: ${FEATURE_ADVANCED_LOGGING:true}
    circuit-breaker:
      enabled: ${FEATURE_CIRCUIT_BREAKER:true}
    rate-limiting:
      enabled: ${FEATURE_RATE_LIMITING:true}
    brute-force-protection:
      enabled: ${FEATURE_BRUTE_FORCE_PROTECTION:true}
    sql-injection-protection:
      enabled: ${FEATURE_SQL_INJECTION_PROTECTION:true}
    xss-protection:
      enabled: ${FEATURE_XSS_PROTECTION:true}
    csrf-protection:
      enabled: ${FEATURE_CSRF_PROTECTION:true}
    input-validation:
      enabled: ${FEATURE_INPUT_VALIDATION:true}
    output-encoding:
      enabled: ${FEATURE_OUTPUT_ENCODING:true}
    secure-headers:
      enabled: ${FEATURE_SECURE_HEADERS:true}
