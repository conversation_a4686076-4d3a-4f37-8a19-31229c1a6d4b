package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.User;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.types.Types.AccountStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.aetrust.repositories.UserRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

@Slf4j
@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    public UserResult getUserById(String userId) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                return UserResult.error("invalid user id", "INVALID_USER_ID");
            }

            Query query = new Query(Criteria.where("id").is(userId)
                .and("deletedAt").isNull()
                .and("accountStatus").ne(AccountStatus.CLOSED));
            User user = mongoTemplate.findOne(query, User.class);

            if (user == null) {
                return UserResult.error("user not found", "USER_NOT_FOUND");
            }

            Map<String, Object> userData = createUserData(user);
            return UserResult.success("user found", userData);

        } catch (Exception error) {
            log.error("Error getting user by ID: {}", error.getMessage());
            return UserResult.error("failed to get user", "INTERNAL_ERROR");
        }
    }
    
    public UpdateResult updateProfile(String userId, UpdateProfileRequest request) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                return UpdateResult.error("invalid user id", "INVALID_USER_ID");
            }

            Query userQuery = new Query(Criteria.where("id").is(userId).and("deletedAt").isNull());
            User user = mongoTemplate.findOne(userQuery, User.class);

            if (user == null) {
                return UpdateResult.error("user not found", "USER_NOT_FOUND");
            }

            Update update = new Update();
            List<String> updatedFields = new ArrayList<>();

            if (request.getFirstName() != null && !request.getFirstName().trim().isEmpty()) {
                update.set("firstName", request.getFirstName().trim());
                updatedFields.add("first_name");
            }

            if (request.getLastName() != null && !request.getLastName().trim().isEmpty()) {
                update.set("lastName", request.getLastName().trim());
                updatedFields.add("last_name");
            }

            if (request.getUsername() != null && !request.getUsername().trim().isEmpty()) {
                Query usernameQuery = new Query(Criteria.where("username").is(request.getUsername().trim())
                    .and("id").ne(userId).and("deletedAt").isNull());
                if (mongoTemplate.exists(usernameQuery, User.class)) {
                    return UpdateResult.error("username already taken", "USERNAME_EXISTS");
                }
                update.set("username", request.getUsername().trim());
                updatedFields.add("username");
            }

            if (request.getBio() != null) {
                update.set("bio", request.getBio().trim());
                updatedFields.add("bio");
            }

            if (request.getDateOfBirth() != null) {
                update.set("dateOfBirth", request.getDateOfBirth());
                updatedFields.add("date_of_birth");
            }

            if (request.getAddress() != null) {
                User.Address address = new User.Address();
                address.setStreet(request.getAddress().getStreet());
                address.setCity(request.getAddress().getCity());
                address.setState(request.getAddress().getState());
                address.setCountry(request.getAddress().getCountry());
                address.setPostalCode(request.getAddress().getPostalCode());

                update.set("address", address);
                updatedFields.add("address");
            }

            update.set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(userQuery, update, User.class);

            return UpdateResult.success("profile updated successfully", updatedFields);

        } catch (Exception error) {
            log.error("Error updating profile: {}", error.getMessage());
            return UpdateResult.error("failed to update profile", "INTERNAL_ERROR");
        }
    }
    
    public PasswordUpdateResult updatePassword(String userId, UpdatePasswordRequest request) {
        try {
            Query query = new Query(Criteria.where("id").is(userId));
            User user = mongoTemplate.findOne(query, User.class);
            
            if (user == null) {
                return PasswordUpdateResult.error("user not found", "USER_NOT_FOUND");
            }
            
            // verify current password
            if (!cryptoUtils.verifyPassword(request.getCurrentPassword(), user.getPassword())) {
                return PasswordUpdateResult.error("current password is incorrect", "INVALID_PASSWORD");
            }
            
            // hash new password
            String newPasswordHash = cryptoUtils.hashPassword(request.getNewPassword());
            
            Update update = new Update()
                .set("password", newPasswordHash)
                .set("updatedAt", LocalDateTime.now());
            
            mongoTemplate.updateFirst(query, update, User.class);
            
            log.info("Password updated for user: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));
            
            return PasswordUpdateResult.success("password updated");
            
        } catch (Exception error) {
            log.error("Error updating password: {}", error.getMessage());
            return PasswordUpdateResult.error("password update failed", "INTERNAL_ERROR");
        }
    }
    
    public UploadResult uploadProfilePicture(String userId, UploadProfilePictureRequest request) {
        try {
            // validate file type and size
            if (!isValidImageType(request.getFileType())) {
                return UploadResult.error("invalid file type", "INVALID_FILE_TYPE");
            }
            
            if (request.getFileSize() > 10485760) { // 10MB
                return UploadResult.error("file too large", "FILE_TOO_LARGE");
            }
            
            // simulate file upload - in production, upload to cloud storage
            String fileUrl = simulateFileUpload(request);
            
            Query query = new Query(Criteria.where("id").is(userId));
            Update update = new Update()
                .set("profilePicture", fileUrl)
                .set("updatedAt", LocalDateTime.now());
            
            mongoTemplate.updateFirst(query, update, User.class);
            
            return UploadResult.success("profile picture uploaded", fileUrl);
            
        } catch (Exception error) {
            log.error("Error uploading profile picture: {}", error.getMessage());
            return UploadResult.error("upload failed", "INTERNAL_ERROR");
        }
    }
    
    public DeletionResult deleteAccount(String userId, DeleteAccountRequest request) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                return DeletionResult.error("invalid user id", "INVALID_USER_ID");
            }

            Query query = new Query(Criteria.where("id").is(userId).and("deletedAt").isNull());
            User user = mongoTemplate.findOne(query, User.class);

            if (user == null) {
                return DeletionResult.error("user not found", "USER_NOT_FOUND");
            }

            if (!cryptoUtils.verifyPassword(request.getPassword(), user.getPassword())) {
                return DeletionResult.error("password is incorrect", "INVALID_PASSWORD");
            }

            // soft delete like TypeScript version
            LocalDateTime now = LocalDateTime.now();
            String maskedEmail = "deleted_" + now.toEpochSecond(java.time.ZoneOffset.UTC) + "_" + user.getEmail();
            String maskedPhone = "deleted_" + now.toEpochSecond(java.time.ZoneOffset.UTC) + "_" + user.getPhone();

            Update update = new Update()
                .set("deletedAt", now)
                .set("accountStatus", AccountStatus.CLOSED)
                .set("email", maskedEmail)
                .set("phone", maskedPhone)
                .set("deletionReason", request.getReason())
                .set("updatedAt", now);

            mongoTemplate.updateFirst(query, update, User.class);

            log.info("Account deleted for user: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));

            return DeletionResult.success("account deleted successfully", true, now);

        } catch (Exception error) {
            log.error("Error deleting account: {}", error.getMessage());
            return DeletionResult.error("failed to delete account", "INTERNAL_ERROR");
        }
    }
    
    public SearchResult searchUsers(String query, int page, int limit) {
        try {
            // limit search results for security
            if (limit > 50) {
                limit = 50;
            }
            
            Query searchQuery = new Query();
            searchQuery.addCriteria(
                new Criteria().orOperator(
                    Criteria.where("firstName").regex(query, "i"),
                    Criteria.where("lastName").regex(query, "i"),
                    Criteria.where("username").regex(query, "i")
                )
            );
            
            // exclude sensitive fields
            searchQuery.fields()
                .include("id", "firstName", "lastName", "username", "profilePicture")
                .exclude("password", "email", "phone");
            
            long totalCount = mongoTemplate.count(searchQuery, User.class);
            
            searchQuery.skip((long) (page - 1) * limit).limit(limit);
            List<User> users = mongoTemplate.find(searchQuery, User.class);
            
            List<Map<String, Object>> userList = new ArrayList<>();
            for (User user : users) {
                Map<String, Object> userData = new HashMap<>();
                userData.put("id", user.getId());
                userData.put("firstName", user.getFirstName());
                userData.put("lastName", user.getLastName());
                userData.put("username", user.getUsername());
                userData.put("profilePicture", user.getProfilePicture());
                userList.add(userData);
            }
            
            int totalPages = (int) Math.ceil((double) totalCount / limit);
            boolean hasMore = page < totalPages;
            
            return SearchResult.success("search completed", userList, totalCount, totalPages, hasMore);
            
        } catch (Exception error) {
            log.error("Error searching users: {}", error.getMessage());
            return SearchResult.error("search failed", "INTERNAL_ERROR");
        }
    }
    
    // helper methods
    private Map<String, Object> createUserData(User user) {
        Map<String, Object> userData = new HashMap<>();
        userData.put("id", user.getId());
        userData.put("email", user.getEmail());
        userData.put("phone", user.getPhone());
        userData.put("username", user.getUsername());
        userData.put("first_name", user.getFirstName());
        userData.put("last_name", user.getLastName());
        userData.put("full_name", user.getFullName());
        userData.put("profile_picture", user.getProfilePicture());
        userData.put("bio", user.getBio());
        userData.put("address", user.getAddress());
        userData.put("role", user.getRole().getValue());
        userData.put("is_verified", user.isVerified());
        userData.put("kyc_status", user.getKycStatus().getValue());
        userData.put("wallet_balance", user.getWalletBalance());
        userData.put("agent_info", user.getAgentInfo());
        userData.put("created_at", user.getCreatedAt());
        userData.put("updated_at", user.getUpdatedAt());
        return userData;
    }
    
    private boolean isValidImageType(String fileType) {
        return fileType.equals("image/jpeg") || 
               fileType.equals("image/jpg") || 
               fileType.equals("image/png");
    }
    
    private String simulateFileUpload(UploadProfilePictureRequest request) {

        String fileId = cryptoUtils.generateSecureToken(16);
        return "https://cdn.aetrust.com/profile-pictures/" + fileId + ".jpg";
    }

    // result classes
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Map<String, Object> user;

        public static UserResult success(String message, Map<String, Object> user) {
            return new UserResult(true, message, null, user);
        }

        public static UserResult error(String message, String errorCode) {
            return new UserResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateResult {
        private boolean success;
        private String message;
        private String errorCode;
        private List<String> updatedFields;

        public static UpdateResult success(String message, List<String> updatedFields) {
            return new UpdateResult(true, message, null, updatedFields);
        }

        public static UpdateResult error(String message, String errorCode) {
            return new UpdateResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordUpdateResult {
        private boolean success;
        private String message;
        private String errorCode;

        public static PasswordUpdateResult success(String message) {
            return new PasswordUpdateResult(true, message, null);
        }

        public static PasswordUpdateResult error(String message, String errorCode) {
            return new PasswordUpdateResult(false, message, errorCode);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UploadResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String fileUrl;

        public static UploadResult success(String message, String fileUrl) {
            return new UploadResult(true, message, null, fileUrl);
        }

        public static UploadResult error(String message, String errorCode) {
            return new UploadResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeletionResult {
        private boolean success;
        private String message;
        private String errorCode;
        private boolean deletionScheduled;
        private LocalDateTime finalDeletionDate;

        public static DeletionResult success(String message, boolean deletionScheduled, LocalDateTime finalDeletionDate) {
            return new DeletionResult(true, message, null, deletionScheduled, finalDeletionDate);
        }

        public static DeletionResult error(String message, String errorCode) {
            return new DeletionResult(false, message, errorCode, false, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchResult {
        private boolean success;
        private String message;
        private String errorCode;
        private List<Map<String, Object>> users;
        private long totalCount;
        private int totalPages;
        private boolean hasMore;

        public static SearchResult success(String message, List<Map<String, Object>> users,
                                         long totalCount, int totalPages, boolean hasMore) {
            return new SearchResult(true, message, null, users, totalCount, totalPages, hasMore);
        }

        public static SearchResult error(String message, String errorCode) {
            return new SearchResult(false, message, errorCode, null, 0, 0, false);
        }
    }
}
