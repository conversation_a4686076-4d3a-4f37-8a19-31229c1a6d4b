package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.User;
import com.aetrust.types.Types.*;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.utils.JwtUtils;
import com.aetrust.utils.RetryUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.aetrust.repositories.UserRepository;
// import org.springframework.data.redis.core.RedisTemplate; // Redis disabled
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RegistrationService {
    
    @Autowired
    private UserRepository userRepository;
    
    // Global cache service - auto-switches between Redis and in-memory
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ObjectMapper objectMapper;
    
    public RegistrationResult initiateRegistration(RegistrationInitRequest request) {
        try {
            // if user already exists
            Query emailQuery = new Query(Criteria.where("email").is(request.getEmail()));
            Query phoneQuery = new Query(Criteria.where("phone").is(request.getPhone()));
            
            if (mongoTemplate.exists(emailQuery, User.class)) {
                return RegistrationResult.error("email already registered", "EMAIL_EXISTS");
            }
            
            if (mongoTemplate.exists(phoneQuery, User.class)) {
                return RegistrationResult.error("phone number already registered", "PHONE_EXISTS");
            }
            
            // create registration session
            String registrationId = cryptoUtils.generateSecureToken(32);
            RegistrationSession session = new RegistrationSession();
            session.setRegistrationId(registrationId);
            session.setEmail(request.getEmail());
            session.setPhone(request.getPhone());
            session.setFirstName(request.getFirstName());
            session.setLastName(request.getLastName());
            session.setPasswordHash(cryptoUtils.hashPassword(request.getPassword()));
            session.setUserType(request.getUserType());
            session.setPlatform(request.getPlatform());
            session.setDateOfBirth(request.getDateOfBirth());
            session.setCurrentStep(RegistrationStep.PHONE_VERIFICATION);
            session.setCreatedAt(System.currentTimeMillis());
            session.setExpiresAt(System.currentTimeMillis() + 900000); // 15 minutes
            

            String sessionKey = "registration:" + registrationId;
            String sessionJson = objectMapper.writeValueAsString(session);
            cacheService.set(sessionKey, sessionJson, 15, TimeUnit.MINUTES);

            String verificationCode = cryptoUtils.generateOTP(6);
            String codeKey = "verification:phone:" + request.getPhone();
            cacheService.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
            
            notificationService.sendSMS(request.getPhone(), 
                "Your AeTrust verification code is: " + verificationCode);
            
            log.info("Registration initiated for phone: {}",
                cryptoUtils.maskSensitiveData(request.getPhone(), 3));
            
            return RegistrationResult.success("registration initiated", registrationId, 
                RegistrationStep.PHONE_VERIFICATION.getValue());
                
        } catch (Exception error) {
            log.error("Error initiating registration: {}", error.getMessage());
            return RegistrationResult.error("registration initiation failed", "INTERNAL_ERROR");
        }
    }
    
    public VerificationResult verifyPhone(PhoneVerificationRequest request) {
        try {

            String codeKey = "verification:phone:" + request.getPhone();
            String storedCode = cacheService.get(codeKey);
            
            if (storedCode == null) {
                return VerificationResult.error("verification code expired", "CODE_EXPIRED");
            }
            
            if (!cryptoUtils.constantTimeCompare(request.getCode(), storedCode)) {
                return VerificationResult.error("invalid verification code", "INVALID_CODE");
            }
            
            RegistrationSession session = findRegistrationSession(request.getPhone());
            if (session == null) {
                return VerificationResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            session.setPhoneVerified(true);
            session.setCurrentStep(RegistrationStep.EMAIL_VERIFICATION);
            updateRegistrationSession(session);
            
            cacheService.delete(codeKey);

            String emailCode = cryptoUtils.generateOTP(6);
            String emailCodeKey = "verification:email:" + session.getEmail();
            cacheService.set(emailCodeKey, emailCode, 5, TimeUnit.MINUTES);
            
            notificationService.sendEmail(session.getEmail(), 
                "AeTrust Email Verification", 
                "Your verification code is: " + emailCode);
            
            return VerificationResult.success("phone verified", session.getRegistrationId(), 
                RegistrationStep.EMAIL_VERIFICATION.getValue());
                
        } catch (Exception error) {
            log.error("Error verifying phone: {}", error.getMessage());
            return VerificationResult.error("phone verification failed", "INTERNAL_ERROR");
        }
    }
    
    public VerificationResult verifyEmail(EmailVerificationRequest request) {
        try {

            String codeKey = "verification:email:" + request.getEmail();
            String storedCode = cacheService.get(codeKey);
            
            if (storedCode == null) {
                return VerificationResult.error("verification code expired", "CODE_EXPIRED");
            }
            
            if (!cryptoUtils.constantTimeCompare(request.getCode(), storedCode)) {
                return VerificationResult.error("invalid verification code", "INVALID_CODE");
            }
            

            RegistrationSession session = findRegistrationSessionByEmail(request.getEmail());
            if (session == null) {
                return VerificationResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            session.setEmailVerified(true);
            session.setCurrentStep(RegistrationStep.TRANSACTION_PIN);
            updateRegistrationSession(session);
            
            cacheService.delete(codeKey);
            
            return VerificationResult.success("email verified", session.getRegistrationId(), 
                RegistrationStep.TRANSACTION_PIN.getValue());
                
        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return VerificationResult.error("email verification failed", "INTERNAL_ERROR");
        }
    }
    
    public CompletionResult completeRegistration(RegistrationCompleteRequest request) {
        try {

            RegistrationSession session = findRegistrationSession(request.getPhone());
            if (session == null) {
                return CompletionResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            if (!session.isPhoneVerified() || !session.isEmailVerified()) {
                return CompletionResult.error("verification not completed", "VERIFICATION_INCOMPLETE");
            }
            
            User user = new User();
            user.setEmail(session.getEmail());
            user.setPhone(session.getPhone());
            user.setPassword(session.getPasswordHash());
            user.setFirstName(session.getFirstName());
            user.setLastName(session.getLastName());
            user.setDateOfBirth(session.getDateOfBirth());
            user.setRole(UserRole.USER);
            user.setAccountStatus(AccountStatus.ACTIVE);
            user.setRegistrationStep(RegistrationStep.COMPLETED);
            user.setPhoneVerified(true);
            user.setEmailVerified(true);
            user.setTransactionPinSet(true);
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            
            String pinHash = cryptoUtils.hashPassword(request.getPin());
            user.setTransactionPin(pinHash);

            User savedUser = RetryUtils.withDatabaseRetry(() -> {
                return userRepository.save(user);
            }, "user-registration-save");
            
            String accessToken = jwtUtils.generateToken(
                savedUser.getId(), savedUser.getEmail(), savedUser.getRole(),
                savedUser.isVerified(), savedUser.getKycStatus());
            String refreshToken = jwtUtils.generateRefreshToken(savedUser.getId());
            
            Map<String, Object> userProfile = createUserProfile(savedUser);
            
            cleanupRegistrationSession(session.getRegistrationId());
            
            log.info("Registration completed for user: {}",
                cryptoUtils.maskSensitiveData(savedUser.getEmail(), 2));
            
            return CompletionResult.success("registration completed", accessToken, 
                refreshToken, userProfile, true);
                
        } catch (Exception error) {
            log.error("Error completing registration: {}", error.getMessage());
            return CompletionResult.error("registration completion failed", "INTERNAL_ERROR");
        }
    }
    
    public StatusResult getRegistrationStatus(String phone) {
        try {
            RegistrationSession session = findRegistrationSession(phone);
            if (session == null) {
                return StatusResult.notFound();
            }
            
            return StatusResult.found(session.getCurrentStep().getValue(), 
                session.isPhoneVerified(), session.isEmailVerified(), 
                session.getCurrentStep() == RegistrationStep.COMPLETED, 
                session.getExpiresAt());
                
        } catch (Exception error) {
            log.error("Error getting registration status: {}", error.getMessage());
            return StatusResult.notFound();
        }
    }
    
    public ResendResult resendVerificationCode(ResendCodeRequest request) {
        try {
            RegistrationSession session = findRegistrationSession(request.getPhone());
            if (session == null) {
                return ResendResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            String verificationCode = cryptoUtils.generateOTP(6);
            
            if ("sms".equals(request.getCodeType())) {
                String codeKey = "verification:phone:" + request.getPhone();
                cacheService.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);

                notificationService.sendSMS(request.getPhone(),
                    "Your AeTrust verification code is: " + verificationCode);

                return ResendResult.success("SMS code sent", "sms");

            } else if ("email".equals(request.getCodeType())) {
                String codeKey = "verification:email:" + session.getEmail();
                cacheService.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
                
                notificationService.sendEmail(session.getEmail(), 
                    "AeTrust Email Verification", 
                    "Your verification code is: " + verificationCode);
                    
                return ResendResult.success("Email code sent", "email");
            }
            
            return ResendResult.error("invalid code type", "INVALID_CODE_TYPE");
            
        } catch (Exception error) {
            log.error("Error resending verification code: {}", error.getMessage());
            return ResendResult.error("failed to resend code", "INTERNAL_ERROR");
        }
    }


    private RegistrationSession findRegistrationSession(String phone) {
        try {

            String pattern = "registration:*";
            for (String key : cacheService.keys(pattern)) {
                String sessionData = cacheService.get(key);
                if (sessionData != null) {
                    RegistrationSession session = objectMapper.readValue(sessionData, RegistrationSession.class);
                    if (phone.equals(session.getPhone())) {
                        return session;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Error finding registration session: {}", e.getMessage());
            return null;
        }
    }

    private RegistrationSession findRegistrationSessionByEmail(String email) {
        try {
            String pattern = "registration:*";
            for (String key : cacheService.keys(pattern)) {
                String sessionData = cacheService.get(key);
                if (sessionData != null) {
                    RegistrationSession session = objectMapper.readValue(sessionData, RegistrationSession.class);
                    if (email.equals(session.getEmail())) {
                        return session;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Error finding registration session by email: {}", e.getMessage());
            return null;
        }
    }

    private void updateRegistrationSession(RegistrationSession session) {
        try {
            String sessionKey = "registration:" + session.getRegistrationId();
            String sessionJson = objectMapper.writeValueAsString(session);
            cacheService.set(sessionKey, sessionJson, 15, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("Error updating registration session: {}", e.getMessage());
        }
    }

    private void cleanupRegistrationSession(String registrationId) {
        try {
            String sessionKey = "registration:" + registrationId;
            cacheService.delete(sessionKey);
        } catch (Exception e) {
            log.error("Error cleaning up registration session: {}", e.getMessage());
        }
    }

    private Map<String, Object> createUserProfile(User user) {
        Map<String, Object> profile = new HashMap<>();
        profile.put("userId", user.getId());
        profile.put("email", user.getEmail());
        profile.put("phoneNumber", user.getPhone());
        profile.put("firstName", user.getFirstName());
        profile.put("lastName", user.getLastName());
        profile.put("fullName", user.getFullName());
        profile.put("userRole", user.getRole().getValue());
        profile.put("isVerified", user.isVerified());
        profile.put("kycStatus", user.getKycStatus().getValue());
        profile.put("walletBalance", user.getWalletBalance());
        profile.put("accountCreated", user.getCreatedAt());
        return profile;
    }



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegistrationSession {
        private String registrationId;
        private String email;
        private String phone;
        private String firstName;
        private String lastName;
        private String passwordHash;
        private String userType;
        private String platform;
        private java.time.LocalDate dateOfBirth;
        private RegistrationStep currentStep;
        private boolean phoneVerified = false;
        private boolean emailVerified = false;
        private long createdAt;
        private long expiresAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegistrationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;

        public static RegistrationResult success(String message, String registrationId, String nextStep) {
            return new RegistrationResult(true, message, null, registrationId, nextStep);
        }

        public static RegistrationResult error(String message, String errorCode) {
            return new RegistrationResult(false, message, errorCode, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;
        private boolean completed = false;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userProfile;

        public static VerificationResult success(String message, String registrationId, String nextStep) {
            return new VerificationResult(true, message, null, registrationId, nextStep, false, null, null, null);
        }

        public static VerificationResult error(String message, String errorCode) {
            return new VerificationResult(false, message, errorCode, null, null, false, null, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompletionResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userProfile;
        private boolean walletCreated;

        public static CompletionResult success(String message, String accessToken, String refreshToken,
                                            Map<String, Object> userProfile, boolean walletCreated) {
            return new CompletionResult(true, message, null, accessToken, refreshToken, userProfile, walletCreated);
        }

        public static CompletionResult error(String message, String errorCode) {
            return new CompletionResult(false, message, errorCode, null, null, null, false);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusResult {
        private boolean found;
        private String currentStep;
        private boolean phoneVerified;
        private boolean emailVerified;
        private boolean completed;
        private long expiresAt;

        public static StatusResult found(String currentStep, boolean phoneVerified, boolean emailVerified,
                                       boolean completed, long expiresAt) {
            return new StatusResult(true, currentStep, phoneVerified, emailVerified, completed, expiresAt);
        }

        public static StatusResult notFound() {
            return new StatusResult(false, null, false, false, false, 0);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResendResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String codeType;

        public static ResendResult success(String message, String codeType) {
            return new ResendResult(true, message, null, codeType);
        }

        public static ResendResult error(String message, String errorCode) {
            return new ResendResult(false, message, errorCode, null);
        }
    }
}
